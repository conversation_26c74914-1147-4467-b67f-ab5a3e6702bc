# Greek Terminal Expiration Feature

## Overview

The Greek Terminal executable includes a built-in subscription renewal mechanism that automatically expires after one month of use. This feature is designed to support subscription-based licensing.

## How It Works

### First Run Detection
- When the executable runs for the first time, it creates a hidden timestamp file in the system's temporary directory
- The file is named `.gt_install_data` and contains the first run date and executable path
- This file is stored in a location like `C:\Users\<USER>\AppData\Local\Temp\` on Windows

### Expiration Timeline
- **Default Expiration**: 30 days from first run
- **Warning Period**: Shows warning messages 7 days before expiration
- **After Expiration**: Application refuses to start and shows expiration message

### Self-Deletion Feature
- When the application expires, it attempts to delete itself from the user's computer
- On Windows, this is accomplished using a temporary batch script that runs after the main process exits
- The self-deletion only occurs when running as a compiled executable (not when running as a Python script)

## Configuration Options

The expiration behavior can be configured using environment variables (primarily for testing):

### Environment Variables
- `GT_EXPIRATION_DAYS`: Number of days until expiration (default: 30)
- `GT_EXPIRATION_MINUTES`: For testing - number of minutes until expiration (overrides days)
- `GT_WARNING_DAYS`: Number of days before expiration to show warnings (default: 7)

### Examples
```bash
# Test with 2-minute expiration
set GT_EXPIRATION_MINUTES=2
GreekTerminal.exe

# Test with 7-day expiration and 2-day warning
set GT_EXPIRATION_DAYS=7
set GT_WARNING_DAYS=2
GreekTerminal.exe
```

## Testing the Expiration Feature

A comprehensive test script is provided: `test_expiration.py`

### Running Tests
```bash
python test_expiration.py
```

### Test Scenarios Available
1. **Immediate Expiration** - Expires in 1 minute (logic test only)
2. **Warning Message** - Shows warning after 1.5 minutes, expires in 2 minutes
3. **Normal Operation** - Standard 30-day expiration with 7-day warning
4. **Simulated Old Installation** - Fake 29-day-old installation (shows warning)
5. **Simulated Expired Installation** - Fake 31-day-old installation (expires immediately)
6. **Full Server Test** - Tests with actual server startup (2-minute expiration)
7. **Clear Test Data** - Resets all test data

### Test Data Management
- Test data is stored in the same location as production data
- Use option 7 to clear test data and reset to fresh state
- The test script can simulate different installation ages for comprehensive testing

## User Experience

### Warning Messages
- Appears as a Windows message box (on Windows) or console message (on other platforms)
- Shows number of days remaining and expiration date
- Allows the application to continue running

### Expiration Messages
- Appears as a Windows message box explaining the license has expired
- Provides instructions to contact support for renewal
- Application exits after the message is dismissed

### Self-Deletion
- Occurs automatically after expiration message is shown
- Only happens when running as a compiled executable
- Uses a separate process to ensure clean deletion

## Technical Implementation

### File Locations
- **Data File**: `%TEMP%\.gt_install_data` (Windows) or `/tmp/.gt_install_data` (Unix)
- **Cleanup Script**: `%TEMP%\gt_cleanup.bat` (Windows only, temporary)

### Security Considerations
- The timestamp file is stored in a standard temporary directory
- Users with administrative access could potentially modify or delete the file
- The expiration check happens on every startup
- Self-deletion requires appropriate file system permissions

### Error Handling
- All expiration-related operations fail silently if they encounter errors
- If the timestamp file cannot be created, the application continues to run
- If self-deletion fails, the application still exits but the file remains

## Deployment Notes

### Building the Executable
- The expiration functionality is automatically included when building with `build_exe.py`
- No additional configuration is required for standard deployment
- The feature is only active when running as a compiled executable

### Distribution
- Each distributed executable will have its own 30-day expiration period
- The expiration timer starts when the user first runs the executable
- No server-side validation is required

### Renewal Process
- When a subscription is renewed, provide the user with a new executable
- The new executable will have a fresh 30-day period
- Old executables will continue to be expired and self-delete

## Troubleshooting

### Common Issues
1. **Application won't start**: Check if 30 days have passed since first use
2. **Warning messages not appearing**: Check system date/time settings
3. **Self-deletion not working**: Verify file system permissions

### Manual Reset (for testing)
To manually reset the expiration timer:
1. Navigate to your system's temp directory
2. Delete the `.gt_install_data` file
3. Restart the application

### Debugging
- Set `GT_EXPIRATION_MINUTES=60` to test with 1-hour expiration
- Use the test script to verify functionality before deployment
- Check the console output for any error messages during startup
