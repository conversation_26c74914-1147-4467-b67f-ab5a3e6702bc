# Greek Terminal License System Configuration
# Copy this file to .env and fill in your actual values

# Required Configuration
GITHUB_TOKEN=your_fine_grained_github_token_here
GITHUB_REPO=your_username/your_repo_name
LICENSE_SESSION_SECRET=generate_a_random_secret_key_here

# Optional Configuration
LICENSE_FILE_PATH=licenses/license_data.enc
LICENSE_CACHE_HOURS=1
LICENSE_OFFLINE_GRACE_HOURS=24
LICENSE_SESSION_TIMEOUT_HOURS=24
LICENSE_MAX_LOGIN_ATTEMPTS=5
LICENSE_LOCKOUT_MINUTES=15
LICENSE_ADMIN_USERS=admin,administrator,your_username

# Feature Flags
ENABLE_LICENSE_SYSTEM=true
ENABLE_TRIAL_FALLBACK=true
ENABLE_OFFLINE_MODE=true

# Logging
LOG_LICENSE_ATTEMPTS=true
LOG_ADMIN_ACTIONS=true
