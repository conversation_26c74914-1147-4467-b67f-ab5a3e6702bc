<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Greek Terminal - Authentication</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        /* Greek Terminal Theme Variables */
        :root {
            --bg-primary: #000000;
            --bg-secondary: #1a1a1a;
            --bg-tertiary: #333333;
            --bg-chart: #121212;

            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.15);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);

            --text-primary: #ffffff;
            --text-secondary: #e0e0e0;
            --text-tertiary: #b0b0b0;
            --text-muted: #808080;

            --accent-primary: #ffffff;
            --accent-success: #C0C0C0;
            --accent-danger: #FF0000;
            --accent-warning: #cccccc;

            --border-color: rgba(255, 255, 255, 0.15);
            --border-hover: rgba(255, 255, 255, 0.25);

            --transition-fast: 0.15s ease-out;
            --transition-normal: 0.3s ease-out;
            --transition-slow: 0.5s ease-out;

            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.6);
        }

        body {
            background: linear-gradient(135deg, var(--bg-primary) 0%, #1a1a1a 50%, var(--bg-primary) 100%);
            color: var(--text-primary);
            font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
            line-height: 1.6;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
            position: relative;
        }

        /* Animated background particles */
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.01) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .login-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: var(--glass-shadow);
            overflow: hidden;
            max-width: 480px;
            width: 100%;
            position: relative;
            z-index: 1;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        }

        .login-header {
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
            color: var(--text-primary);
            padding: 3rem 2rem 2rem;
            text-align: center;
            position: relative;
            border-bottom: 1px solid var(--border-color);
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-success), var(--accent-primary));
            border-radius: 0 0 4px 4px;
        }

        .terminal-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--accent-success);
            text-shadow: 0 0 20px rgba(192, 192, 192, 0.3);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .login-header h1 {
            margin: 0;
            font-size: 2.2rem;
            font-weight: 700;
            letter-spacing: -0.5px;
            background: linear-gradient(135deg, var(--text-primary), var(--text-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-header .subtitle {
            margin-top: 0.5rem;
            color: var(--text-tertiary);
            font-size: 0.95rem;
            font-weight: 500;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .login-body {
            padding: 2.5rem;
            background: transparent;
        }

        .form-floating {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-control {
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
            padding: 1.2rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            transition: all var(--transition-normal);
            box-shadow: var(--shadow-sm);
            font-family: 'Inter', monospace;
            letter-spacing: 1px;
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.12);
            border-color: var(--accent-success);
            color: var(--text-primary);
            box-shadow: 0 0 0 3px rgba(192, 192, 192, 0.15), var(--shadow-md);
            transform: translateY(-2px);
            outline: none;
        }

        .form-control:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--border-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .form-control::placeholder {
            color: var(--text-muted);
            font-style: italic;
        }

        .form-floating > label {
            color: var(--text-tertiary);
            font-weight: 600;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
            border: 2px solid var(--accent-success);
            border-radius: 16px;
            padding: 1.2rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            width: 100%;
            color: var(--accent-success);
            transition: all var(--transition-normal);
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(192, 192, 192, 0.1), transparent);
            transition: left var(--transition-fast);
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 25px rgba(192, 192, 192, 0.3), var(--shadow-lg);
            background: linear-gradient(135deg, var(--bg-tertiary) 0%, #404040 100%);
            border-color: var(--accent-success);
            color: var(--accent-success);
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:active {
            transform: translateY(-1px);
            box-shadow: 0 0 15px rgba(192, 192, 192, 0.2), var(--shadow-md);
        }

        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: var(--shadow-sm);
            cursor: not-allowed;
        }

        .alert {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
            padding: 1rem 1.5rem;
            color: var(--text-primary);
        }

        .alert-danger {
            border-left: 4px solid var(--accent-danger);
            background: rgba(255, 0, 0, 0.1);
        }

        .alert-success {
            border-left: 4px solid var(--accent-success);
            background: rgba(192, 192, 192, 0.1);
        }

        .alert-info {
            border-left: 4px solid var(--accent-primary);
            background: rgba(255, 255, 255, 0.1);
        }

        .license-info {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 1.5rem;
            margin-top: 1.5rem;
            color: var(--text-tertiary);
            position: relative;
        }

        .license-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-success), var(--accent-primary));
            border-radius: 16px 16px 0 0;
        }

        .license-info strong {
            color: var(--text-primary);
            font-weight: 700;
        }

        .license-info code {
            background: var(--bg-secondary);
            color: var(--accent-success);
            padding: 0.3rem 0.6rem;
            border-radius: 6px;
            font-family: 'Inter', monospace;
            font-weight: 600;
            letter-spacing: 1px;
            border: 1px solid var(--border-color);
        }

        .loading-spinner {
            display: none;
        }

        .loading .loading-spinner {
            display: inline-block;
        }

        .loading .btn-text {
            display: none;
        }

        .footer-links {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .footer-links a {
            color: var(--text-tertiary);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all var(--transition-normal);
            padding: 0.5rem 1rem;
            border-radius: 8px;
        }

        .footer-links a:hover {
            color: var(--accent-success);
            background: rgba(192, 192, 192, 0.1);
            text-decoration: none;
            transform: translateY(-1px);
        }

        /* Modal styling */
        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        /* Responsive design */
        @media (max-width: 576px) {
            .login-card {
                margin: 1rem;
                border-radius: 20px;
            }

            .login-header {
                padding: 2rem 1.5rem 1.5rem;
            }

            .login-body {
                padding: 2rem 1.5rem;
            }

            .terminal-logo {
                font-size: 2.5rem;
            }

            .login-header h1 {
                font-size: 1.8rem;
            }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--glass-border);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--border-hover);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="terminal-logo">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h1>Greek Terminal</h1>
                <div class="subtitle">Professional Options Analytics Platform</div>
            </div>
            
            <div class="login-body">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form id="loginForm" method="POST" action="{{ url_for('login') }}">
                    <div class="form-floating">
                        <input type="text"
                               class="form-control"
                               id="username"
                               name="username"
                               placeholder="Enter your username"
                               pattern="[a-zA-Z0-9_]+"
                               title="Username can only contain letters, numbers, and underscores"
                               autocomplete="username"
                               spellcheck="false"
                               required>
                        <label for="username">
                            <i class="fas fa-user me-2"></i>Username
                        </label>
                    </div>

                    <div class="form-floating">
                        <input type="text"
                               class="form-control"
                               id="license_key"
                               name="license_key"
                               placeholder="DEV-XXXXX-XXXX"
                               pattern="DEV-\d{5}-[A-F0-9]{4}"
                               title="License key format: DEV-XXXXX-XXXX"
                               autocomplete="off"
                               spellcheck="false"
                               required>
                        <label for="license_key">
                            <i class="fas fa-key me-2"></i>License Authentication
                        </label>
                    </div>

                    <button type="submit" class="btn btn-login" id="loginBtn">
                        <span class="btn-text">
                            <i class="fas fa-terminal me-2"></i>Access Terminal
                        </span>
                        <span class="loading-spinner">
                            <i class="fas fa-spinner fa-spin me-2"></i>Authenticating...
                        </span>
                    </button>
                </form>
                
                <div class="license-info">
                    <div class="mb-3">
                        <strong><i class="fas fa-shield-alt me-2"></i>Authentication Protocol</strong>
                    </div>
                    <div class="mb-2">
                        <small>Username:</small> <code>Your registered username</code>
                    </div>
                    <div class="mb-2">
                        <small>License Format:</small> <code>DEV-XXXXX-XXXX</code>
                    </div>
                    <div class="mt-3">
                        <small><i class="fas fa-lock me-1"></i>Secure GitHub-based license validation with Discord integration</small>
                    </div>
                </div>
                
                <div class="footer-links">
                    <a href="#" onclick="showContactInfo()">
                        <i class="fas fa-headset me-1"></i>Support
                    </a>
                    <span class="mx-3" style="color: var(--text-muted);">•</span>
                    <a href="#" onclick="showLicenseInfo()">
                        <i class="fas fa-info-circle me-1"></i>Documentation
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Modal -->
    <div class="modal fade" id="contactModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border); border-radius: 16px;">
                <div class="modal-header" style="background: var(--bg-secondary); border-bottom: 1px solid var(--border-color); border-radius: 16px 16px 0 0;">
                    <h5 class="modal-title" style="color: var(--text-primary);">
                        <i class="fas fa-headset me-2" style="color: var(--accent-success);"></i>Technical Support
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" style="color: var(--text-primary);">
                    <p style="color: var(--text-secondary);">For technical assistance with Greek Terminal:</p>
                    <div class="mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fab fa-discord me-3" style="color: #5865F2; font-size: 1.2rem;"></i>
                            <div>
                                <strong>Discord Support</strong><br>
                                <small style="color: var(--text-tertiary);">Join our community server for real-time help</small><br>
                                <a href="https://discord.gg/QuB4qvMXmC" target="_blank" class="btn btn-sm mt-2"
                                   style="background: #5865F2; color: white; border: none; border-radius: 6px; padding: 0.4rem 0.8rem; text-decoration: none; font-size: 0.85rem;">
                                    <i class="fab fa-discord me-1"></i>Join Discord Server
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fab fa-github me-3" style="color: var(--text-primary); font-size: 1.2rem;"></i>
                            <div>
                                <strong>GitHub Repository</strong><br>
                                <small style="color: var(--text-tertiary);">Report issues and view documentation</small>
                            </div>
                        </div>
                    </div>
                    <div class="alert" style="background: rgba(192, 192, 192, 0.1); border-left: 3px solid var(--accent-success); color: var(--text-primary);">
                        <i class="fas fa-key me-2"></i>
                        <strong>License Issues:</strong> Verify your license key format and GitHub repository access
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- License Info Modal -->
    <div class="modal fade" id="licenseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border); border-radius: 16px;">
                <div class="modal-header" style="background: var(--bg-secondary); border-bottom: 1px solid var(--border-color); border-radius: 16px 16px 0 0;">
                    <h5 class="modal-title" style="color: var(--text-primary);">
                        <i class="fas fa-shield-alt me-2" style="color: var(--accent-success);"></i>License System Documentation
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" style="color: var(--text-primary);">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 style="color: var(--accent-success);">Authentication Requirements:</h6>
                            <p><strong>Username:</strong> Your registered username</p>
                            <p><strong>License Key:</strong> <code style="background: var(--bg-secondary); color: var(--accent-success); padding: 0.3rem 0.6rem; border-radius: 6px;">DEV-XXXXX-XXXX</code></p>
                            <ul style="color: var(--text-secondary);">
                                <li><code>DEV</code> - Product identifier</li>
                                <li><code>XXXXX</code> - 5-digit license number</li>
                                <li><code>XXXX</code> - 4-character hex verification</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 style="color: var(--accent-success);">Security Features:</h6>
                            <ul style="color: var(--text-secondary);">
                                <li>Username + license validation</li>
                                <li>GitHub-based license storage</li>
                                <li>Discord ID integration</li>
                                <li>Encrypted license validation</li>
                                <li>Session management</li>
                            </ul>
                        </div>
                    </div>

                    <hr style="border-color: var(--border-color);">

                    <h6 style="color: var(--accent-success);">Terminal Features:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul style="color: var(--text-secondary);">
                                <li>Real-time options analytics</li>
                                <li>Advanced Greeks calculations</li>
                                <li>Gamma exposure analysis</li>
                                <li>Volatility surface mapping</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul style="color: var(--text-secondary);">
                                <li>Professional charting tools</li>
                                <li>Risk management metrics</li>
                                <li>Market regime detection</li>
                                <li>Export capabilities</li>
                            </ul>
                        </div>
                    </div>

                    <hr style="border-color: var(--border-color);">

                    <h6 style="color: var(--accent-success);">Ownership & Legal Information:</h6>
                    <div class="ownership-info" style="color: var(--text-secondary);">
                        <p><strong style="color: var(--text-primary);">Greek Terminal</strong> and all associated pseudonyms, including but not limited to:</p>
                        <ul>
                            <li><strong>Greek Terminal</strong> - Primary trading platform</li>
                            <li><strong>Options Analytics Terminal</strong> - Alternative designation</li>
                            <li><strong>Professional Options Analytics Platform</strong> - Extended name</li>
                            <li><strong>GT Platform</strong> - Abbreviated reference</li>
                            <li><strong>Terminal Analytics</strong> - Simplified designation</li>
                            <li><strong>stackedeth</strong> - Discord ID: 1330639231500157149</li>
                            <li><strong>legogrievous</strong> - Discord ID: 564235489155088459</li>
                            <li><strong>vanagostine</strong> - Discord ID: 1087480323979350156</li>
                            <li><strong>akugo_v2</strong> - Discord ID: 758321783731781643</li>
                        </ul>
                        <p style="margin-top: 1rem;">
                            <strong style="color: var(--text-primary);">Copyright Notice:</strong><br>
                            All intellectual property, source code, algorithms, methodologies, and associated materials are the exclusive property of the Greek Terminal development team.
                            This includes all derivatives, modifications, and implementations of the platform under any name or pseudonym.
                        </p>
                        <p>
                            <strong style="color: var(--text-primary);">License Agreement:</strong><br>
                            By accessing this platform, users acknowledge and agree to the proprietary nature of all content and functionality.
                            Unauthorized reproduction, distribution, or reverse engineering is strictly prohibited.
                        </p>
                        <div style="background: var(--bg-secondary); padding: 0.75rem; border-radius: 8px; margin-top: 1rem; border-left: 3px solid var(--accent-success);">
                            <small style="color: var(--text-primary);">
                                <i class="fas fa-copyright me-2"></i>
                                <strong>© 2025 Greek Terminal Development Team. All Rights Reserved.</strong>
                            </small>
                        </div>
                    </div>

                    <hr style="border-color: var(--border-color);">

                    <h6 style="color: var(--accent-warning);">
                        <i class="fas fa-exclamation-triangle me-2"></i>Important Disclaimer & Risk Warning
                    </h6>
                    <div class="disclaimer-section" style="color: var(--text-secondary);">
                        <div style="background: rgba(255, 193, 7, 0.1); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">
                            <p style="margin-bottom: 0.75rem; color: var(--text-primary);">
                                <strong style="color: var(--accent-warning);">FINANCIAL RISK WARNING:</strong><br>
                                Trading options and financial derivatives involves substantial risk of loss and is not suitable for all investors.
                                Past performance does not guarantee future results. You may lose some or all of your invested capital.
                            </p>
                        </div>

                        <h6 style="color: var(--text-primary); margin-top: 1.5rem;">Limitation of Liability:</h6>
                        <ul style="margin-bottom: 1rem;">
                            <li><strong>No Financial Responsibility:</strong> Greek Terminal and its developers are not responsible for any financial losses, trading losses, or investment decisions made using this platform.</li>
                            <li><strong>Data Accuracy:</strong> While we strive for accuracy, we make no warranties regarding the completeness, accuracy, or timeliness of market data, calculations, or analytics.</li>
                            <li><strong>System Availability:</strong> We do not guarantee uninterrupted service and are not liable for any losses due to system downtime, technical issues, or connectivity problems.</li>
                            <li><strong>Third-Party Data:</strong> Market data is provided by third-party sources. We are not responsible for errors, delays, or interruptions in such data.</li>
                        </ul>

                        <h6 style="color: var(--text-primary);">User Responsibilities:</h6>
                        <ul style="margin-bottom: 1rem;">
                            <li><strong>Independent Decision Making:</strong> All trading and investment decisions are solely your responsibility. Consult with qualified financial advisors before making investment decisions.</li>
                            <li><strong>Risk Management:</strong> You are responsible for implementing appropriate risk management strategies and position sizing.</li>
                            <li><strong>Regulatory Compliance:</strong> Ensure your use of this platform complies with all applicable laws and regulations in your jurisdiction.</li>
                            <li><strong>Account Security:</strong> Maintain the confidentiality of your login credentials and license information.</li>
                        </ul>

                        <h6 style="color: var(--text-primary);">Platform Limitations:</h6>
                        <ul style="margin-bottom: 1rem;">
                            <li><strong>Educational Tool:</strong> This platform is designed as an analytical and educational tool, not as investment advice or trading recommendations.</li>
                            <li><strong>No Guarantees:</strong> No guarantees are made regarding the profitability or success of any trading strategies or market analysis.</li>
                            <li><strong>Market Risks:</strong> Financial markets are inherently volatile and unpredictable. Past performance does not indicate future results.</li>
                            <li><strong>Technical Analysis:</strong> All technical indicators, Greeks calculations, and analytical tools are for informational purposes only.</li>
                        </ul>

                        <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); border-radius: 8px; padding: 1rem; margin-top: 1rem;">
                            <p style="margin-bottom: 0; color: var(--text-primary);">
                                <strong style="color: var(--accent-danger);">
                                    <i class="fas fa-shield-alt me-2"></i>LEGAL DISCLAIMER:
                                </strong><br>
                                By using Greek Terminal, you acknowledge that you have read, understood, and agree to these terms.
                                You use this platform entirely at your own risk and agree to hold harmless Greek Terminal, its developers,
                                and associated entities from any and all claims, losses, damages, or liabilities arising from your use of this platform.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form submission handling
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const btn = document.getElementById('loginBtn');
            btn.classList.add('loading');
            btn.disabled = true;
        });

        // Auto-format license key input
        document.getElementById('license_key').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase().replace(/[^A-Z0-9-]/g, '');
            
            // Auto-format as DEV-XXXXX-XXXX
            if (value.length > 0 && !value.startsWith('DEV-')) {
                if (value.startsWith('DEV')) {
                    value = 'DEV-' + value.substring(3);
                } else {
                    value = 'DEV-' + value;
                }
            }

            // Add dashes at appropriate positions
            if (value.length > 9 && value.charAt(9) !== '-') {
                value = value.substring(0, 9) + '-' + value.substring(9);
            }

            // Limit length to 14 characters (DEV-12345-ABCD)
            if (value.length > 14) {
                value = value.substring(0, 14);
            }
            
            e.target.value = value;
        });

        // Modal functions
        function showContactInfo() {
            new bootstrap.Modal(document.getElementById('contactModal')).show();
        }

        function showLicenseInfo() {
            new bootstrap.Modal(document.getElementById('licenseModal')).show();
        }

        // Auto-focus username input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });

        // Handle Enter key to move between fields
        document.getElementById('username').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('license_key').focus();
            }
        });

        document.getElementById('license_key').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('loginForm').submit();
            }
        });
    </script>

    <!-- Copyright Footer -->
    <footer class="copyright-footer">
        <div class="container-fluid">
            <div class="text-center text-muted">
                <small>Copyright © 2025 Greek Terminal. All rights reserved.</small>
            </div>
        </div>
    </footer>

    <style>
        .copyright-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(33, 37, 41, 0.95);
            border-top: 1px solid #495057;
            padding: 8px 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .copyright-footer small {
            font-size: 0.75rem;
            color: #6c757d;
        }

        /* Add bottom padding to body to prevent content overlap */
        body {
            padding-bottom: 40px;
        }
    </style>
</body>
</html>
