#!/usr/bin/env python3
"""
License Administration Tools for Greek Terminal
Provides tools for managing licenses in GitHub repository
"""

import os
import json
import random
import string
from datetime import datetime, timedelta
from license_manager import LicenseManager, LicenseConfig
import re

# Import Discord webhook
try:
    from discord_webhook import get_discord_webhook
    DISCORD_WEBHOOK_AVAILABLE = True
except ImportError:
    print("⚠️ Discord webhook not available")
    DISCORD_WEBHOOK_AVAILABLE = False

class LicenseAdmin:
    """Administrative tools for license management"""
    
    def __init__(self):
        self.manager = LicenseManager()
        self.config = LicenseConfig()
    
    def generate_license_key(self):
        """Generate a new license key in DEV-XXXXX-XXXX format"""
        # Generate 5-digit number (10000-99999 = always 5 digits)
        number = random.randint(10000, 99999)

        # Generate 4-character hex string (uppercase)
        hex_chars = ''.join(random.choices('ABCDEF0123456789', k=4))

        return f"DEV-{number}-{hex_chars}"
    
    def create_license_line(self, discord_id, username, expiry_date, license_key=None):
        """Create a license line in the required format"""
        if not license_key:
            license_key = self.generate_license_key()
        
        # Format expiry date
        if isinstance(expiry_date, str):
            # Try to parse if it's a string
            try:
                expiry_date = datetime.strptime(expiry_date, '%Y/%m/%d')
            except ValueError:
                try:
                    expiry_date = datetime.strptime(expiry_date, '%Y-%m-%d')
                except ValueError:
                    raise ValueError("Invalid date format. Use YYYY/MM/DD or YYYY-MM-DD")
        
        expiry_str = expiry_date.strftime('%Y/%m/%d')
        
        # Validate username (alphanumeric and underscore only)
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            raise ValueError("Username can only contain letters, numbers, and underscores")
        
        # Validate discord_id (should be numeric)
        if not str(discord_id).isdigit():
            raise ValueError("Discord ID must be numeric")
        
        return f"{license_key}|{discord_id}|{username}|{expiry_str}"
    
    def load_current_licenses(self):
        """Load current licenses from GitHub"""
        try:
            if not self.manager._repo:
                raise Exception("GitHub repository not configured")
            
            # Try to get existing file
            try:
                file_content = self.manager._repo.get_contents(self.config.LICENSE_FILE_PATH)
                encrypted_data = file_content.decoded_content.decode('utf-8')
                decrypted_data = self.manager._decrypt_data(encrypted_data)
                
                if decrypted_data and 'licenses' in decrypted_data:
                    return decrypted_data['licenses']
                else:
                    return []
            except Exception:
                # File doesn't exist or is corrupted, start with empty list
                return []
                
        except Exception as e:
            print(f"Error loading current licenses: {e}")
            return []
    
    def save_licenses_to_github(self, license_lines, commit_message="Update licenses"):
        """Save license lines to GitHub repository"""
        try:
            if not self.manager._repo:
                raise Exception("GitHub repository not configured")
            
            # Prepare data for encryption
            license_data = {
                'licenses': license_lines,
                'last_updated': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            # Encrypt the data
            encrypted_data = self.manager._encrypt_data(license_data)
            if not encrypted_data:
                raise Exception("Failed to encrypt license data")
            
            # Check if file exists
            try:
                file_content = self.manager._repo.get_contents(self.config.LICENSE_FILE_PATH)
                # Update existing file
                self.manager._repo.update_file(
                    path=self.config.LICENSE_FILE_PATH,
                    message=commit_message,
                    content=encrypted_data,
                    sha=file_content.sha
                )
            except Exception:
                # Create new file
                self.manager._repo.create_file(
                    path=self.config.LICENSE_FILE_PATH,
                    message=commit_message,
                    content=encrypted_data
                )
            
            print(f"✅ Licenses saved to GitHub: {len(license_lines)} licenses")
            return True
            
        except Exception as e:
            print(f"❌ Error saving licenses to GitHub: {e}")
            return False
    
    def add_license(self, discord_id, username, days_valid=30, license_key=None):
        """Add a new license"""
        try:
            # Calculate expiry date
            expiry_date = datetime.now() + timedelta(days=days_valid)
            
            # Create license line
            license_line = self.create_license_line(discord_id, username, expiry_date, license_key)
            
            # Load current licenses
            current_licenses = self.load_current_licenses()
            
            # Check for duplicates
            new_license_key = license_line.split('|')[0]
            for existing_line in current_licenses:
                if existing_line.startswith(new_license_key + '|'):
                    raise Exception(f"License key {new_license_key} already exists")
                
                # Check for duplicate discord_id
                parts = existing_line.split('|')
                if len(parts) >= 2 and parts[1] == str(discord_id):
                    raise Exception(f"Discord ID {discord_id} already has a license")
            
            # Add new license
            current_licenses.append(license_line)
            
            # Save to GitHub
            if self.save_licenses_to_github(current_licenses, f"Add license for {username}"):
                print(f"✅ License added: {license_line}")

                # Invalidate license cache to ensure new license is available immediately
                try:
                    from license_manager import invalidate_license_cache
                    invalidate_license_cache()
                    print(f"🔄 License cache invalidated for immediate availability")
                except Exception as e:
                    print(f"⚠️ Cache invalidation failed: {e}")

                # Send Discord notification
                if DISCORD_WEBHOOK_AVAILABLE:
                    try:
                        webhook = get_discord_webhook()
                        webhook.notify_license_created(
                            license_key=new_license_key,
                            discord_id=str(discord_id),
                            username=username,
                            days_valid=days_valid,
                            expiry_date=expiry_date.strftime('%Y/%m/%d')
                        )
                    except Exception as e:
                        print(f"⚠️ Discord notification failed: {e}")

                return {
                    'success': True,
                    'license_key': new_license_key,
                    'license_line': license_line
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to save to GitHub'
                }
                
        except Exception as e:
            print(f"❌ Error adding license: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def remove_license(self, license_key):
        """Remove a license by license key"""
        try:
            # Load current licenses
            current_licenses = self.load_current_licenses()
            
            # Find and remove the license
            updated_licenses = []
            removed_license = None
            
            for license_line in current_licenses:
                if license_line.startswith(license_key + '|'):
                    removed_license = license_line
                else:
                    updated_licenses.append(license_line)
            
            if not removed_license:
                return {
                    'success': False,
                    'error': f'License key {license_key} not found'
                }
            
            # Save updated licenses
            if self.save_licenses_to_github(updated_licenses, f"Remove license {license_key}"):
                print(f"✅ License removed: {removed_license}")

                # Invalidate license cache
                try:
                    from license_manager import invalidate_license_cache
                    invalidate_license_cache()
                    print(f"🔄 License cache invalidated after removal")
                except Exception as e:
                    print(f"⚠️ Cache invalidation failed: {e}")

                # Send Discord notification
                if DISCORD_WEBHOOK_AVAILABLE:
                    try:
                        webhook = get_discord_webhook()
                        # Extract username from removed license
                        parts = removed_license.split('|')
                        username = parts[2] if len(parts) >= 3 else 'Unknown'
                        webhook.notify_license_removed(
                            license_key=license_key,
                            username=username
                        )
                    except Exception as e:
                        print(f"⚠️ Discord notification failed: {e}")

                return {
                    'success': True,
                    'removed_license': removed_license
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to save to GitHub'
                }
                
        except Exception as e:
            print(f"❌ Error removing license: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def extend_license(self, license_key, additional_days):
        """Extend a license by additional days"""
        try:
            # Load current licenses
            current_licenses = self.load_current_licenses()
            
            # Find and update the license
            updated_licenses = []
            updated_license = None
            
            for license_line in current_licenses:
                if license_line.startswith(license_key + '|'):
                    # Parse the license
                    parts = license_line.split('|')
                    if len(parts) == 4:
                        license_key_part, discord_id, username, expiry_str = parts
                        
                        # Parse current expiry
                        current_expiry = datetime.strptime(expiry_str, '%Y/%m/%d')
                        
                        # Add additional days
                        new_expiry = current_expiry + timedelta(days=additional_days)
                        new_expiry_str = new_expiry.strftime('%Y/%m/%d')
                        
                        # Create updated license line
                        updated_line = f"{license_key_part}|{discord_id}|{username}|{new_expiry_str}"
                        updated_licenses.append(updated_line)
                        updated_license = updated_line
                    else:
                        updated_licenses.append(license_line)
                else:
                    updated_licenses.append(license_line)
            
            if not updated_license:
                return {
                    'success': False,
                    'error': f'License key {license_key} not found'
                }
            
            # Save updated licenses
            if self.save_licenses_to_github(updated_licenses, f"Extend license {license_key} by {additional_days} days"):
                print(f"✅ License extended: {updated_license}")

                # Invalidate license cache
                try:
                    from license_manager import invalidate_license_cache
                    invalidate_license_cache()
                    print(f"🔄 License cache invalidated after extension")
                except Exception as e:
                    print(f"⚠️ Cache invalidation failed: {e}")

                # Send Discord notification
                if DISCORD_WEBHOOK_AVAILABLE:
                    try:
                        webhook = get_discord_webhook()
                        # Extract username and new expiry from updated license
                        parts = updated_license.split('|')
                        username = parts[2] if len(parts) >= 3 else 'Unknown'
                        new_expiry = parts[3] if len(parts) >= 4 else 'Unknown'
                        webhook.notify_license_extended(
                            license_key=license_key,
                            username=username,
                            additional_days=additional_days,
                            new_expiry=new_expiry
                        )
                    except Exception as e:
                        print(f"⚠️ Discord notification failed: {e}")

                return {
                    'success': True,
                    'updated_license': updated_license
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to save to GitHub'
                }
                
        except Exception as e:
            print(f"❌ Error extending license: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def list_licenses(self):
        """List all current licenses with status"""
        try:
            current_licenses = self.load_current_licenses()
            license_list = []

            for license_line in current_licenses:
                license_info = self.manager._parse_license_line(license_line)
                if license_info:
                    # Check if expired
                    now = datetime.now()
                    is_expired = license_info['expiry_date'] < now
                    days_remaining = (license_info['expiry_date'] - now).days if not is_expired else 0

                    license_list.append({
                        'license_key': license_info['license_key'],
                        'discord_id': license_info['discord_id'],
                        'username': license_info['username'],
                        'expiry_date': license_info['expiry_str'],
                        'is_expired': is_expired,
                        'days_remaining': days_remaining,
                        'raw_line': license_line
                    })

            return {
                'success': True,
                'licenses': license_list,
                'total_count': len(license_list)
            }

        except Exception as e:
            print(f"❌ Error listing licenses: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def cleanup_expired_licenses(self):
        """Remove all expired licenses"""
        try:
            # Load current licenses
            current_licenses = self.load_current_licenses()

            # Filter out expired licenses
            active_licenses = []
            expired_licenses = []
            now = datetime.now()

            for license_line in current_licenses:
                license_info = self.manager._parse_license_line(license_line)
                if license_info:
                    if license_info['expiry_date'] >= now:
                        # License is still active
                        active_licenses.append(license_line)
                    else:
                        # License is expired
                        expired_licenses.append(license_line)
                        print(f"🗑️ Expired license found: {license_info['license_key']} ({license_info['username']})")
                else:
                    # Keep malformed lines (shouldn't happen, but safety)
                    active_licenses.append(license_line)

            if not expired_licenses:
                return {
                    'success': True,
                    'message': 'No expired licenses found',
                    'removed_count': 0
                }

            # Save updated licenses (without expired ones)
            if self.save_licenses_to_github(active_licenses, f"Cleanup: Removed {len(expired_licenses)} expired licenses"):
                print(f"✅ Cleaned up {len(expired_licenses)} expired licenses")

                # Invalidate license cache
                try:
                    from license_manager import invalidate_license_cache
                    invalidate_license_cache()
                    print(f"🔄 License cache invalidated after cleanup")
                except Exception as e:
                    print(f"⚠️ Cache invalidation failed: {e}")

                # Send Discord notification
                if DISCORD_WEBHOOK_AVAILABLE:
                    try:
                        webhook = get_discord_webhook()

                        # Create cleanup notification
                        embed = {
                            "title": "🗑️ License Cleanup Completed",
                            "description": f"Removed {len(expired_licenses)} expired licenses from the system",
                            "color": 0xFFA500,  # Orange color
                            "fields": [
                                {
                                    "name": "📊 Cleanup Summary",
                                    "value": f"**Removed**: {len(expired_licenses)} expired licenses\n**Remaining**: {len(active_licenses)} active licenses",
                                    "inline": False
                                }
                            ],
                            "timestamp": datetime.now().isoformat()
                        }

                        payload = {
                            "username": "Greek Terminal",
                            "embeds": [embed]
                        }

                        webhook._send_webhook(payload)
                        print(f"📢 Cleanup notification sent to Discord")
                    except Exception as e:
                        print(f"⚠️ Discord cleanup notification failed: {e}")

                return {
                    'success': True,
                    'message': f'Successfully removed {len(expired_licenses)} expired licenses',
                    'removed_count': len(expired_licenses),
                    'remaining_count': len(active_licenses)
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to save updated licenses to GitHub'
                }

        except Exception as e:
            print(f"❌ Error cleaning up expired licenses: {e}")
            return {
                'success': False,
                'error': str(e)
            }

# Global admin instance
_license_admin = None

def get_license_admin():
    """Get the global license admin instance"""
    global _license_admin
    if _license_admin is None:
        _license_admin = LicenseAdmin()
    return _license_admin
