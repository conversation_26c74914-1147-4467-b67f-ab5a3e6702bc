# Greek Terminal License System Setup Guide

This guide will help you set up the GitHub-based license system for Greek Terminal.

## Overview

The license system provides:
- **Discord-based authentication** using license keys
- **GitHub repository storage** for license data
- **Admin dashboard** for license management
- **Secure encryption** and anti-tampering protection
- **Online/offline validation** with caching

## License Format

Licenses follow this format:
```
DEV-12345-ABCD|123456789012345678|username|2025/07/05
```

Where:
- `DEV-12345-ABCD` = License key
- `123456789012345678` = Discord ID
- `username` = User's login username
- `2025/07/05` = Expiry date

## Prerequisites

1. **GitHub Repository**: You need a GitHub repository to store license data
2. **GitHub Token**: Personal access token with repository permissions
3. **Python Dependencies**: Install required packages

## Step 1: Install Dependencies

```bash
pip install -r requirements.txt
```

The license system requires these additional packages:
- `requests>=2.31.0`
- `PyGithub>=1.59.1`
- `Flask-Session>=0.5.0`

## Step 2: GitHub Setup

### 2.1 Create GitHub Repository

1. Create a new **private** GitHub repository for license storage
2. Note the repository name in format: `username/repo-name`

### 2.2 Generate GitHub Token

1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Generate a new token with these permissions:
   - `repo` (Full control of private repositories)
   - `read:user` (Read user profile data)
3. Copy the token (you won't see it again!)

### 2.3 Repository Structure

Your repository will automatically create this structure:
```
your-repo/
├── licenses/
│   └── license_data.enc    # Encrypted license data
└── README.md
```

## Step 3: Environment Configuration

Create a `.env` file or set environment variables:

```bash
# Required Configuration
GITHUB_TOKEN=your_github_token_here
GITHUB_REPO=username/repo-name
LICENSE_SESSION_SECRET=your_secret_key_here

# Optional Configuration
LICENSE_FILE_PATH=licenses/license_data.enc
LICENSE_CACHE_HOURS=1
LICENSE_OFFLINE_GRACE_HOURS=24
LICENSE_SESSION_TIMEOUT_HOURS=24
LICENSE_MAX_LOGIN_ATTEMPTS=5
LICENSE_LOCKOUT_MINUTES=15
LICENSE_ADMIN_USERS=admin,administrator
ENABLE_LICENSE_SYSTEM=true
ENABLE_TRIAL_FALLBACK=true
```

### Environment Variables Explained

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `GITHUB_TOKEN` | ✅ | - | GitHub personal access token |
| `GITHUB_REPO` | ✅ | - | Repository in format `username/repo` |
| `LICENSE_SESSION_SECRET` | ✅ | - | Secret key for session encryption |
| `LICENSE_FILE_PATH` | ❌ | `licenses/license_data.enc` | Path to license file in repo |
| `LICENSE_CACHE_HOURS` | ❌ | `1` | How long to cache license data |
| `LICENSE_OFFLINE_GRACE_HOURS` | ❌ | `24` | Offline validation period |
| `LICENSE_SESSION_TIMEOUT_HOURS` | ❌ | `24` | User session timeout |
| `LICENSE_MAX_LOGIN_ATTEMPTS` | ❌ | `5` | Max failed login attempts |
| `LICENSE_LOCKOUT_MINUTES` | ❌ | `15` | Lockout duration after max attempts |
| `LICENSE_ADMIN_USERS` | ❌ | `admin,administrator` | Comma-separated admin usernames |

## Step 4: Test the Setup

### 4.1 Test Configuration

```python
from license_config import license_config

# Check configuration
errors = license_config.validate_config()
if errors:
    print("Configuration errors:", errors)
else:
    print("Configuration is valid!")

# View configuration summary
print(license_config.get_config_summary())
```

### 4.2 Test GitHub Connection

```python
from license_manager import get_license_manager

manager = get_license_manager()
licenses = manager.get_licenses()
print("License data loaded:", licenses is not None)
```

## Step 5: Add Your First License

### 5.1 Using Python Script

```python
from license_admin import get_license_admin

admin = get_license_admin()

# Add a license for yourself (admin access)
result = admin.add_license(
    discord_id="123456789012345678",  # Your Discord ID
    username="admin",                 # Your username
    days_valid=365                    # 1 year license
)

if result['success']:
    print(f"License created: {result['license_key']}")
    print(f"Full license line: {result['license_line']}")
else:
    print(f"Error: {result['error']}")
```

### 5.2 Using Admin Dashboard

1. Start the application: `python app.py`
2. Go to: `http://localhost:5000/login`
3. Login with your license key
4. Go to: `http://localhost:5000/admin`
5. Use the admin interface to manage licenses

## Step 6: Application Integration

The license system is already integrated into your Flask app. Key features:

### 6.1 Login Page
- Users access: `http://localhost:5000/login`
- Enter license key in format: `DEV-12345-ABCD`
- Automatic validation and session creation

### 6.2 Admin Dashboard
- Admins access: `http://localhost:5000/admin`
- View all licenses and statistics
- Add, remove, and extend licenses
- Real-time license management

### 6.3 API Protection
- All API endpoints require valid license
- Automatic session validation
- Rate limiting for failed attempts

## Step 7: License Management

### Adding Licenses

```python
from license_admin import get_license_admin

admin = get_license_admin()

# Add different license types
admin.add_license("discord_id", "username", 7)    # 7-day trial
admin.add_license("discord_id", "username", 30)   # Monthly
admin.add_license("discord_id", "username", 365)  # Yearly
```

### Extending Licenses

```python
# Extend a license by 30 days
result = admin.extend_license("DEV-12345-ABCD", 30)
```

### Removing Licenses

```python
# Remove a license
result = admin.remove_license("DEV-12345-ABCD")
```

### Listing Licenses

```python
# Get all licenses with status
result = admin.list_licenses()
for license in result['licenses']:
    print(f"{license['license_key']} - {license['username']} - {license['days_remaining']} days")
```

## Security Features

### Encryption
- All license data is encrypted using Fernet (AES 128)
- System-specific encryption keys
- HMAC validation for data integrity

### Rate Limiting
- Maximum 5 failed login attempts
- 15-minute lockout after max attempts
- IP-based attempt tracking

### Session Security
- Secure session cookies
- Automatic session timeout
- Regular license revalidation

### Anti-Tampering
- Encrypted local cache
- System fingerprinting
- Checksum validation

## Troubleshooting

### Common Issues

1. **"GitHub repository not configured"**
   - Check `GITHUB_TOKEN` and `GITHUB_REPO` environment variables
   - Verify token has repository permissions

2. **"License key not found"**
   - Verify license exists in GitHub repository
   - Check license format: `DEV-XXXXX-XXXX`

3. **"Access denied. Admin privileges required"**
   - Add your username to `LICENSE_ADMIN_USERS`
   - Ensure you're logged in with admin account

4. **"Unable to load license data"**
   - Check internet connection
   - Verify GitHub token permissions
   - Check repository exists and is accessible

### Debug Mode

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Configuration Check

```python
from license_config import license_config
print(license_config.get_config_summary())
```

## Production Deployment

### Environment Variables
Set all required environment variables on your production server.

### Security Considerations
1. Use strong `LICENSE_SESSION_SECRET`
2. Keep GitHub token secure
3. Use private repository for license storage
4. Enable HTTPS in production
5. Regular backup of license data

### Monitoring
- Monitor failed login attempts
- Track license usage and expiration
- Set up alerts for license system errors

## Pseudonyms under Greek Terminal

The following pseudonyms are associated with Greek Terminal development and operations:

| Pseudonym | Discord ID |
|-----------|------------|
| stackedeth | 1330639231500157149 |
| legogrievous | 564235489155088459 |
| vanagostine | 1087480323979350156 |
| akugo_v2 | 758321783731781643 |

These Discord IDs can be used when creating licenses for the development team or when configuring system access.

## Support

If you need help:
1. Check the troubleshooting section
2. Verify your configuration
3. Test with a simple license first
4. Check GitHub repository permissions

## Copyright

Copyright © 2025 Greek Terminal. All rights reserved.
