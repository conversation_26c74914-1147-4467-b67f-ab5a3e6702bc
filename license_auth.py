#!/usr/bin/env python3
"""
License Authentication System for Greek Terminal
Handles user login and session management with license validation
"""

import os
import hashlib
import secrets
from datetime import datetime, timedelta
from flask import session, request, redirect, url_for, flash
from functools import wraps
from license_manager import get_license_manager

# Import Discord webhook
try:
    from discord_webhook import get_discord_webhook
    DISCORD_WEBHOOK_AVAILABLE = True
except ImportError:
    DISCORD_WEBHOOK_AVAILABLE = False

class LicenseAuth:
    """License-based authentication system"""
    
    def __init__(self, app=None):
        self.app = app
        self.license_manager = get_license_manager()
        
        # Session configuration
        self.SESSION_TIMEOUT_HOURS = 24  # Session expires after 24 hours
        self.MAX_LOGIN_ATTEMPTS = 5  # Max failed login attempts
        self.LOCKOUT_DURATION_MINUTES = 15  # Lockout duration after max attempts
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize the authentication system with Flask app"""
        self.app = app
        
        # Configure session
        app.config['SESSION_TYPE'] = 'filesystem'
        app.config['SESSION_PERMANENT'] = False
        app.config['SESSION_USE_SIGNER'] = True
        app.config['SESSION_KEY_PREFIX'] = 'gt_license:'
        
        # Set secret key if not already set
        if not app.config.get('SECRET_KEY'):
            app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', secrets.token_hex(32))
    
    def _get_client_ip(self):
        """Get client IP address"""
        if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
            return request.environ['REMOTE_ADDR']
        else:
            return request.environ['HTTP_X_FORWARDED_FOR']
    
    def _get_login_attempt_key(self, license_key):
        """Generate key for tracking login attempts"""
        client_ip = self._get_client_ip()
        return hashlib.sha256(f"{license_key}:{client_ip}".encode()).hexdigest()
    
    def _check_rate_limit(self, license_key):
        """Check if login attempts are rate limited"""
        attempt_key = self._get_login_attempt_key(license_key)
        session_key = f"login_attempts:{attempt_key}"
        
        attempts_data = session.get(session_key, {'count': 0, 'last_attempt': None})
        
        # Check if lockout period has expired
        if attempts_data.get('last_attempt'):
            last_attempt = datetime.fromisoformat(attempts_data['last_attempt'])
            if datetime.now() - last_attempt > timedelta(minutes=self.LOCKOUT_DURATION_MINUTES):
                # Reset attempts after lockout period
                attempts_data = {'count': 0, 'last_attempt': None}
                session[session_key] = attempts_data
        
        # Check if max attempts exceeded
        if attempts_data['count'] >= self.MAX_LOGIN_ATTEMPTS:
            return False, f"Too many failed attempts. Try again in {self.LOCKOUT_DURATION_MINUTES} minutes."
        
        return True, None
    
    def _record_login_attempt(self, license_key, success):
        """Record a login attempt"""
        attempt_key = self._get_login_attempt_key(license_key)
        session_key = f"login_attempts:{attempt_key}"
        
        if success:
            # Clear attempts on successful login
            session.pop(session_key, None)
        else:
            # Increment failed attempts
            attempts_data = session.get(session_key, {'count': 0, 'last_attempt': None})
            attempts_data['count'] += 1
            attempts_data['last_attempt'] = datetime.now().isoformat()
            session[session_key] = attempts_data
    
    def login_user(self, license_key, username=None):
        """Authenticate user with license key and optional username"""
        try:
            # Check rate limiting
            rate_limit_ok, rate_limit_msg = self._check_rate_limit(license_key)
            if not rate_limit_ok:
                return False, rate_limit_msg

            # Validate license
            validation_result = self.license_manager.validate_license(license_key)

            if not validation_result['valid']:
                self._record_login_attempt(license_key, False)
                error_msg = validation_result.get('error', 'Invalid license')
                return False, error_msg

            # License is valid, check username if provided
            license_info = validation_result['license_info']

            # If username is provided, verify it matches the license
            if username and license_info['username'] != username:
                self._record_login_attempt(license_key, False)
                return False, "Username does not match license"
            
            session['authenticated'] = True
            session['license_key'] = license_key
            session['discord_id'] = license_info['discord_id']
            session['username'] = license_info['username']
            session['expiry_date'] = license_info['expiry_str']
            session['login_time'] = datetime.now().isoformat()
            session['days_remaining'] = validation_result['days_remaining']
            session['license_source'] = validation_result['source']
            
            # Make session permanent with timeout
            session.permanent = True
            
            # Record successful login
            self._record_login_attempt(license_key, True)

            # Send Discord notification for login
            if DISCORD_WEBHOOK_AVAILABLE:
                try:
                    print(f"🔔 Attempting to send login notification for {license_info['username']}")
                    webhook = get_discord_webhook()
                    success = webhook.notify_license_login(
                        license_key=license_key,
                        username=license_info['username'],
                        discord_id=license_info['discord_id'],
                        days_remaining=validation_result['days_remaining']
                    )
                    if success:
                        print(f"✅ Login notification sent successfully for {license_info['username']}")
                    else:
                        print(f"❌ Login notification failed for {license_info['username']}")
                except Exception as e:
                    print(f"⚠️ Discord login notification failed: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print("⚠️ Discord webhook not available for login notification")

            return True, "Login successful"
            
        except Exception as e:
            print(f"Login error: {e}")
            return False, "Login system error"
    
    def logout_user(self):
        """Log out the current user"""
        # Clear all session data
        session.clear()
        return True
    
    def is_authenticated(self):
        """Check if user is authenticated"""
        if not session.get('authenticated'):
            return False
        
        # Check session timeout
        login_time_str = session.get('login_time')
        if login_time_str:
            login_time = datetime.fromisoformat(login_time_str)
            if datetime.now() - login_time > timedelta(hours=self.SESSION_TIMEOUT_HOURS):
                self.logout_user()
                return False
        
        # Optionally re-validate license periodically
        license_key = session.get('license_key')
        if license_key:
            # Re-validate every hour
            last_validation = session.get('last_validation')
            if not last_validation or datetime.now() - datetime.fromisoformat(last_validation) > timedelta(hours=1):
                validation_result = self.license_manager.validate_license(license_key)
                if not validation_result['valid']:
                    self.logout_user()
                    return False
                
                # Update session with fresh data
                session['last_validation'] = datetime.now().isoformat()
                session['days_remaining'] = validation_result['days_remaining']
                session['license_source'] = validation_result['source']
        
        return True
    
    def get_user_info(self):
        """Get current user information"""
        if not self.is_authenticated():
            return None
        
        return {
            'license_key': session.get('license_key'),
            'discord_id': session.get('discord_id'),
            'username': session.get('username'),
            'expiry_date': session.get('expiry_date'),
            'days_remaining': session.get('days_remaining'),
            'login_time': session.get('login_time'),
            'license_source': session.get('license_source', 'unknown')
        }
    
    def require_auth(self, f):
        """Decorator to require authentication for routes"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not self.is_authenticated():
                # Store the original URL to redirect after login
                session['next_url'] = request.url
                return redirect(url_for('login'))
            return f(*args, **kwargs)
        return decorated_function
    
    def require_auth_api(self, f):
        """Decorator to require authentication for API endpoints"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not self.is_authenticated():
                from flask import jsonify
                return jsonify({
                    'success': False,
                    'error': 'Authentication required',
                    'authenticated': False
                }), 401
            return f(*args, **kwargs)
        return decorated_function

# Global auth instance
_license_auth = None

def get_license_auth(app=None):
    """Get the global license auth instance"""
    global _license_auth
    if _license_auth is None:
        _license_auth = LicenseAuth(app)
    return _license_auth

def init_license_auth(app):
    """Initialize license authentication with Flask app"""
    auth = get_license_auth(app)
    return auth

# Convenience functions
def login_required(f):
    """Decorator for routes that require authentication"""
    auth = get_license_auth()
    return auth.require_auth(f)

def api_login_required(f):
    """Decorator for API routes that require authentication"""
    auth = get_license_auth()
    return auth.require_auth_api(f)

def current_user():
    """Get current user information"""
    auth = get_license_auth()
    return auth.get_user_info()

def is_logged_in():
    """Check if user is logged in"""
    auth = get_license_auth()
    return auth.is_authenticated()
