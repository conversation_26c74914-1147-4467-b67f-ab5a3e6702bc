# Greek Terminal - How to Actually Use This Thing

Alright, so you've got Greek Terminal running. This is basically your options flow dashboard that shows you where the smart money is positioned and how market makers are gonna move the market around.

## 🚨 SERIOUSLY - Don't Be That Guy

**Don't close the ugly black command window**

Look, I know that black terminal window looks like garbage and you want to close it. DON'T. That's what's actually running everything. Close it and you'll break the whole thing.

- Keep that black window open (minimize it if it bothers you)
- Only close the browser tab when you're done
- If you mess up and close it, just restart the whole program

## Getting Started (It's Pretty Simple)

1. Double-click `GreekTerminal.exe` 
2. Wait for your browser to open (it does this automatically)
3. Pick a ticker - SPY is already selected and it's probably what you want anyway
4. Choose an expiry date from the dropdown
5. Hit "Refresh All" to load everything

The interface is pretty straightforward - you've got your ticker/expiry controls at the top, and then a bunch of chart tabs below. Each tab shows different options flow data.

## The Charts (What Actually Matters)

### Candlestick Chart
Come on, you know what this is. If you don't, maybe stick to index funds.

### GEX (Gamma Exposure) - The Important One
This is probably the most useful chart. Here's what's happening:

**Green bars = Support levels**
- Market makers have to buy when price goes down, sell when it goes up
- This creates support/resistance at these strikes
- Bigger bars = stronger levels

**Red bars = Danger zones** 
- Market makers amplify moves here (they sell when price drops, buy when it rises)
- Price tends to rip through these levels fast or reject fast, I like to set limit orders here targeting the nearest negitive exposure zone or positive exposure 
- Expect more volatility

**How to actually use it:**
- Buy near big green bars in an uptrend
- Sell near big green bars in a downtrend  
- When price hits red bars, expect it to keep moving in that direction
- The zero line is where things get interesting - that's the flip point

**Example:** SPY is at 400, you see huge green bars at 395 and 405. It's probably gonna chop between those levels.

(NOTE: Market Makers LOVE positive gamma exposure, they will do just about anything to keep price in a positive exposure area, ever wonder why markets tend to balance and range? Positive gamma exposure is it. About 80% of the time price will stay within the positive gamma exposure regime and range. Market makers love positive gamma exposure so much because its the area where they have to work the least hard to earn the most profits. It is easier for them to stay "delta-neutral" in these areas. Positive exposre is a great target)

### VGEX (Volume Gamma) - GEX But Better
Same idea as regular GEX, but only counts options that actually traded today. More accurate but changes throughout the day.

I personally like regular GEX better because it's more stable, but VGEX shows you what's happening right now.

### DEX (Delta Exposure) - Directional Bias
Shows you if people are positioned bullish or bearish:

- **Green bars = Bullish positioning** (people bought calls or sold puts)
- **Red bars = Bearish positioning** (people bought puts or sold calls)

Use this to see which way the smart money is leaning. If there's massive greenn above current price, institutions are betting on a move up.

### The Other Greeks (For Nerds)
- **DGEX**: Delta + Gamma combined. Shows momentum potential.
- **TEX**: Time decay stuff. Green = people selling premium, red = people buying premium.
- **VEGX**: Volatility exposure. Useful before earnings or Fed meetings.
- **VEX**: Vanna exposure. Advanced stuff, ignore unless you really know what you're doing. really usefull for EOD moves, if you want to learn how to use it I suggest youtube 
- **CEX**: Charm exposure. Even more advanced. Also really usefull for EOD moves, if you want to learn how to use it I suggest youtube 

### Open Interest (OI) - Where the Big Money Is
Shows you where open contracts are:

- **Big green bars = Lots of call positions**
- **Big red bars = Lots of put positions**
- **Huge bars = Strong support/resistance**

At expiration, price tends to get "pinned" to strikes with massive open interest.

### GEX Visualizer - Historical Patterns
Shows you how gamma has changed over time.

## Controls That Actually Matter

**"Refresh All" button** - Updates everything with latest data. Use this.

**"Auto Refresh All" toggle** - Updates every 15 seconds automatically. Turn this on if you're actively trading.

**Ticker dropdown** - SPY, QQQ, AAPL, etc. SPY is usually what you want if you are an ES trader, for NQ the correlation is QQQ.

**Expiry dropdown** - Pick your expiration date. Closer dates = more impact on current price.

## Gamma Regime (Top Right Corner)
This tells you what kind of market you're in:

- **Green = Low vol environment** - Market makers provide support, expect chop
- **Red = High vol environment** - Market makers amplify moves, expect trends

## How to Actually Trade This

**For day trading:**
1. Watch GEX levels for support/resistance  
2. Use 1-5 minute candles
3. Buy bounces off green GEX, sell breaks through red GEX

**For swing trading:**
1. Look at weekly/monthly GEX levels with a far away expiry
2. Check DEX for directional bias
3. Use daily candles
4. Position around major gamma levels

**For options trading:**
1. Check all the Greeks together
2. Look at OI for institutional positioning
3. Watch TEX if you're selling premium
4. Use VEGX before earnings/events

## Common Mistakes

- Closing the command window (don't do it, your dashboard will crash. Only close it when you're done trading)
- Not refreshing data
- Overthinking the advanced Greeks (stick to GEX and VGEX if youre a beginner)
- Ignoring the gamma regime indicator
- Trading against massive GEX levels

## If Something Breaks

- Charts not loading? Hit "Refresh All"
- Data looks old? Turn on auto-refresh
- App crashed? Restart the .exe file
- Browser acting weird? Refresh the page

That's pretty much it. Start with GEX and DEX, ignore the fancy stuff until you understand the basics.
