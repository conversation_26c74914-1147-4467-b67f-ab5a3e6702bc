#!/usr/bin/env python3
"""
License System Configuration for Greek Terminal
Environment-based configuration for GitHub license management
"""

import os
from datetime import timedelta

class LicenseSystemConfig:
    """Configuration class for license system"""
    
    # GitHub Configuration
    GITHUB_TOKEN = os.environ.get('GITHUB_TOKEN', '')
    GITHUB_REPO = os.environ.get('GITHUB_REPO', '')  # format: "username/repo"
    
    # License file path in GitHub repository
    LICENSE_FILE_PATH = os.environ.get('LICENSE_FILE_PATH', 'licenses/license_data.enc')
    
    # Security Settings
    ENCRYPTION_ITERATIONS = int(os.environ.get('LICENSE_ENCRYPTION_ITERATIONS', '100000'))
    SALT_LENGTH = 32
    KEY_LENGTH = 32
    
    # Cache Settings
    CACHE_DURATION_HOURS = int(os.environ.get('LICENSE_CACHE_HOURS', '1'))
    OFFLINE_GRACE_PERIOD_HOURS = int(os.environ.get('LICENSE_OFFLINE_GRACE_HOURS', '24'))
    
    # Session Settings
    SESSION_TIMEOUT_HOURS = int(os.environ.get('LICENSE_SESSION_TIMEOUT_HOURS', '24'))
    SESSION_SECRET_KEY = os.environ.get('LICENSE_SESSION_SECRET', '')
    
    # Rate Limiting
    MAX_LOGIN_ATTEMPTS = int(os.environ.get('LICENSE_MAX_LOGIN_ATTEMPTS', '5'))
    LOCKOUT_DURATION_MINUTES = int(os.environ.get('LICENSE_LOCKOUT_MINUTES', '15'))
    
    # Admin Users (comma-separated list)
    ADMIN_USERS = [user.strip() for user in os.environ.get('LICENSE_ADMIN_USERS', 'admin,administrator').split(',')]
    
    # License Validation
    REVALIDATION_INTERVAL_HOURS = int(os.environ.get('LICENSE_REVALIDATION_HOURS', '1'))
    
    # License Format Validation
    LICENSE_PATTERN = r'^DEV-\d{5}-[A-F0-9]{4}\|\d+\|[a-zA-Z0-9_]+\|\d{4}/\d{2}/\d{2}$'
    
    # Default License Durations (in days)
    DEFAULT_TRIAL_DAYS = int(os.environ.get('LICENSE_DEFAULT_TRIAL_DAYS', '7'))
    DEFAULT_MONTHLY_DAYS = int(os.environ.get('LICENSE_DEFAULT_MONTHLY_DAYS', '30'))
    DEFAULT_QUARTERLY_DAYS = int(os.environ.get('LICENSE_DEFAULT_QUARTERLY_DAYS', '90'))
    DEFAULT_YEARLY_DAYS = int(os.environ.get('LICENSE_DEFAULT_YEARLY_DAYS', '365'))
    
    # Feature Flags
    ENABLE_LICENSE_SYSTEM = os.environ.get('ENABLE_LICENSE_SYSTEM', 'true').lower() == 'true'
    ENABLE_TRIAL_FALLBACK = os.environ.get('ENABLE_TRIAL_FALLBACK', 'true').lower() == 'true'
    ENABLE_OFFLINE_MODE = os.environ.get('ENABLE_OFFLINE_MODE', 'true').lower() == 'true'
    
    # Logging
    LOG_LICENSE_ATTEMPTS = os.environ.get('LOG_LICENSE_ATTEMPTS', 'true').lower() == 'true'
    LOG_ADMIN_ACTIONS = os.environ.get('LOG_ADMIN_ACTIONS', 'true').lower() == 'true'
    
    @classmethod
    def validate_config(cls):
        """Validate that required configuration is present"""
        errors = []
        
        if not cls.GITHUB_TOKEN:
            errors.append("GITHUB_TOKEN environment variable is required")
        
        if not cls.GITHUB_REPO:
            errors.append("GITHUB_REPO environment variable is required (format: username/repo)")
        
        if not cls.SESSION_SECRET_KEY:
            errors.append("LICENSE_SESSION_SECRET environment variable is required")
        
        if '/' not in cls.GITHUB_REPO:
            errors.append("GITHUB_REPO must be in format 'username/repo'")
        
        return errors
    
    @classmethod
    def get_config_summary(cls):
        """Get a summary of current configuration (for debugging)"""
        return {
            'github_repo': cls.GITHUB_REPO,
            'license_file_path': cls.LICENSE_FILE_PATH,
            'cache_duration_hours': cls.CACHE_DURATION_HOURS,
            'offline_grace_hours': cls.OFFLINE_GRACE_PERIOD_HOURS,
            'session_timeout_hours': cls.SESSION_TIMEOUT_HOURS,
            'max_login_attempts': cls.MAX_LOGIN_ATTEMPTS,
            'lockout_duration_minutes': cls.LOCKOUT_DURATION_MINUTES,
            'admin_users': cls.ADMIN_USERS,
            'enable_license_system': cls.ENABLE_LICENSE_SYSTEM,
            'enable_trial_fallback': cls.ENABLE_TRIAL_FALLBACK,
            'enable_offline_mode': cls.ENABLE_OFFLINE_MODE,
            'github_token_configured': bool(cls.GITHUB_TOKEN),
            'session_secret_configured': bool(cls.SESSION_SECRET_KEY)
        }

# Create a default configuration instance
license_config = LicenseSystemConfig()

# Validate configuration on import
config_errors = license_config.validate_config()
if config_errors:
    print("⚠️ License System Configuration Errors:")
    for error in config_errors:
        print(f"   - {error}")
    print("   License system may not function properly without proper configuration.")
else:
    print("✅ License system configuration validated successfully")
