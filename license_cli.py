#!/usr/bin/env python3
"""
Command Line Interface for Greek Terminal License Management
Simple CLI tool for managing licenses
"""

import sys
import os
import argparse
from datetime import datetime, timedelta

def setup_environment():
    """Setup environment from .env file if available"""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment loaded from .env file")
    except ImportError:
        print("⚠️ python-dotenv not installed, using system environment variables")
    except Exception as e:
        print(f"⚠️ Could not load .env file: {e}")

def validate_discord_id(discord_id):
    """Validate Discord ID format"""
    if not discord_id.isdigit():
        raise ValueError("Discord ID must be numeric")
    if len(discord_id) < 17 or len(discord_id) > 19:
        raise ValueError("Discord ID should be 17-19 digits long")
    return discord_id

def validate_username(username):
    """Validate username format"""
    import re
    if not re.match(r'^[a-zA-Z0-9_]+$', username):
        raise ValueError("Username can only contain letters, numbers, and underscores")
    return username

def add_license(args):
    """Add a new license"""
    try:
        from license_admin import get_license_admin
        
        # Validate inputs
        discord_id = validate_discord_id(args.discord_id)
        username = validate_username(args.username)
        
        admin = get_license_admin()
        result = admin.add_license(discord_id, username, args.days)
        
        if result['success']:
            print("✅ License created successfully!")
            print(f"🔑 License Key: {result['license_key']}")
            print(f"👤 Username: {username}")
            print(f"🆔 Discord ID: {discord_id}")
            print(f"📅 Valid for: {args.days} days")
            print(f"📋 Full License Line: {result['license_line']}")
        else:
            print(f"❌ Failed to create license: {result['error']}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

def remove_license(args):
    """Remove a license"""
    try:
        from license_admin import get_license_admin
        
        admin = get_license_admin()
        result = admin.remove_license(args.license_key)
        
        if result['success']:
            print("✅ License removed successfully!")
            print(f"🗑️ Removed: {result['removed_license']}")
        else:
            print(f"❌ Failed to remove license: {result['error']}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

def extend_license(args):
    """Extend a license"""
    try:
        from license_admin import get_license_admin
        
        admin = get_license_admin()
        result = admin.extend_license(args.license_key, args.days)
        
        if result['success']:
            print("✅ License extended successfully!")
            print(f"⏰ Extended: {result['updated_license']}")
        else:
            print(f"❌ Failed to extend license: {result['error']}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

def list_licenses(args):
    """List all licenses"""
    try:
        from license_admin import get_license_admin
        
        admin = get_license_admin()
        result = admin.list_licenses()
        
        if result['success']:
            licenses = result['licenses']
            
            if not licenses:
                print("📝 No licenses found")
                return
            
            print(f"📋 Found {len(licenses)} license(s):")
            print()
            
            # Print header
            print(f"{'License Key':<15} {'Username':<15} {'Discord ID':<20} {'Expiry':<12} {'Status':<12} {'Days Left':<10}")
            print("-" * 95)
            
            # Sort by expiry date
            licenses.sort(key=lambda x: x['expiry_date'])
            
            for license in licenses:
                status = "EXPIRED" if license['is_expired'] else "ACTIVE"
                days_left = "0" if license['is_expired'] else str(license['days_remaining'])
                
                print(f"{license['license_key']:<15} {license['username']:<15} {license['discord_id']:<20} "
                      f"{license['expiry_date']:<12} {status:<12} {days_left:<10}")
            
            # Print summary
            active_count = sum(1 for l in licenses if not l['is_expired'])
            expired_count = len(licenses) - active_count
            
            print()
            print(f"📊 Summary: {active_count} active, {expired_count} expired")
            
        else:
            print(f"❌ Failed to list licenses: {result['error']}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

def validate_license(args):
    """Validate a license key"""
    try:
        from license_manager import get_license_manager
        
        manager = get_license_manager()
        result = manager.validate_license(args.license_key)
        
        if result['valid']:
            info = result['license_info']
            print("✅ License is valid!")
            print(f"🔑 License Key: {info['license_key']}")
            print(f"👤 Username: {info['username']}")
            print(f"🆔 Discord ID: {info['discord_id']}")
            print(f"📅 Expires: {info['expiry_str']}")
            print(f"⏰ Days remaining: {result['days_remaining']}")
            print(f"📡 Source: {result['source']}")
        else:
            print(f"❌ License is invalid: {result['error']}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

def check_config(args):
    """Check license system configuration"""
    try:
        from license_config import license_config

        print("🔧 License System Configuration:")
        print()

        # Check for errors
        errors = license_config.validate_config()
        if errors:
            print("❌ Configuration Errors:")
            for error in errors:
                print(f"   - {error}")
            print()
        else:
            print("✅ Configuration is valid")
            print()

        # Show configuration summary
        config_summary = license_config.get_config_summary()

        print("📋 Configuration Summary:")
        for key, value in config_summary.items():
            if 'token' in key.lower() or 'secret' in key.lower():
                # Hide sensitive values
                display_value = "***configured***" if value else "***not set***"
            else:
                display_value = value

            print(f"   {key}: {display_value}")

        # Check Discord webhook configuration
        print("\n🔔 Discord Webhook Configuration:")
        webhook_url = os.environ.get('DISCORD_WEBHOOK_URL')
        webhook_enabled = os.environ.get('DISCORD_WEBHOOK_ENABLED', 'false').lower() == 'true'

        print(f"   Webhook URL: {'✅ Configured' if webhook_url else '❌ Not configured'}")
        print(f"   Webhook Enabled: {'✅ Yes' if webhook_enabled else '❌ No'}")
        print(f"   Login Notifications: {'✅ Yes' if os.environ.get('DISCORD_NOTIFY_LOGINS', 'false').lower() == 'true' else '❌ No'}")

    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        sys.exit(1)

def test_webhook(args):
    """Test Discord webhook"""
    try:
        print("🧪 Testing Discord Webhook...")

        from discord_webhook import test_webhook

        success = test_webhook()
        if success:
            print("✅ Discord webhook test successful!")
            print("📢 Check your Discord channel for the test message!")
        else:
            print("❌ Discord webhook test failed!")
            sys.exit(1)

    except Exception as e:
        print(f"❌ Discord webhook test error: {e}")
        sys.exit(1)

def main():
    """Main CLI function"""
    setup_environment()
    
    parser = argparse.ArgumentParser(
        description="Greek Terminal License Management CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s add 123456789012345678 john_doe 30
  %(prog)s list
  %(prog)s validate DEV-12345-ABCD
  %(prog)s extend DEV-12345-ABCD 30
  %(prog)s remove DEV-12345-ABCD
  %(prog)s config
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Add license command
    add_parser = subparsers.add_parser('add', help='Add a new license')
    add_parser.add_argument('discord_id', help='Discord ID (numeric)')
    add_parser.add_argument('username', help='Username (alphanumeric + underscore)')
    add_parser.add_argument('days', type=int, help='License duration in days')
    add_parser.set_defaults(func=add_license)
    
    # Remove license command
    remove_parser = subparsers.add_parser('remove', help='Remove a license')
    remove_parser.add_argument('license_key', help='License key to remove')
    remove_parser.set_defaults(func=remove_license)
    
    # Extend license command
    extend_parser = subparsers.add_parser('extend', help='Extend a license')
    extend_parser.add_argument('license_key', help='License key to extend')
    extend_parser.add_argument('days', type=int, help='Additional days')
    extend_parser.set_defaults(func=extend_license)
    
    # List licenses command
    list_parser = subparsers.add_parser('list', help='List all licenses')
    list_parser.set_defaults(func=list_licenses)
    
    # Validate license command
    validate_parser = subparsers.add_parser('validate', help='Validate a license key')
    validate_parser.add_argument('license_key', help='License key to validate')
    validate_parser.set_defaults(func=validate_license)
    
    # Check configuration command
    config_parser = subparsers.add_parser('config', help='Check system configuration')
    config_parser.set_defaults(func=check_config)

    # Test webhook command
    webhook_parser = subparsers.add_parser('webhook', help='Test Discord webhook')
    webhook_parser.set_defaults(func=test_webhook)
    
    # Parse arguments
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Execute command
    args.func(args)

if __name__ == '__main__':
    main()
