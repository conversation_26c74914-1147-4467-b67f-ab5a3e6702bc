# Greek Terminal - Professional Options Analytics Platform

A comprehensive license management system and options analytics platform for professional traders and developers.

## Overview

Greek Terminal is a sophisticated web-based platform that provides real-time options analytics, advanced Greeks calculations, and professional-grade trading tools. The system features a robust license management system with GitHub integration and Discord notifications.

## Features

### 🔐 License Management System
- **Secure Authentication**: GitHub-based license validation with Discord integration
- **Developer Hierarchy**: Role-based access control with developer management
- **Real-time Notifications**: Discord webhook integration for license events
- **Admin Panel**: Comprehensive license and user management interface

### 📊 Options Analytics Platform
- **Real-time Data**: Live options chain analysis and market data
- **Advanced Greeks**: Delta, Gamma, Theta, Vega, Vanna, and Charm calculations
- **Gamma Exposure Analysis**: GEX, VGEX, and Delta-Gamma exposure mapping
- **Volatility Analysis**: Surface mapping and skew analysis
- **Professional Charting**: Interactive charts with TradingView integration

### 🛠️ Technical Features
- **Modern Web Interface**: Responsive design with dark theme
- **Real-time Updates**: WebSocket integration for live data
- **Export Capabilities**: TradingView and data export functionality
- **Security**: Encrypted license storage and secure session management

## System Architecture

### License System
- **GitHub Integration**: Secure license storage in GitHub repositories
- **Discord Notifications**: Real-time alerts for license events
- **Role Management**: Developer promotion and protection system
- **Session Management**: Secure user authentication and session handling

### Analytics Engine
- **Options Data Processing**: Real-time options chain analysis
- **Greeks Calculations**: Advanced mathematical models for risk metrics
- **Market Regime Detection**: Gamma regime analysis and alerts
- **Data Visualization**: Professional charting and analytics display

## Installation & Setup

### Prerequisites
- Python 3.8+
- GitHub repository access
- Discord webhook (optional)

### Environment Configuration
Create a `.env` file with the following configuration:

```env
# GitHub Configuration
GITHUB_TOKEN=your_github_token_here
GITHUB_REPO=username/repository-name

# Discord Integration (Optional)
DISCORD_WEBHOOK_URL=your_discord_webhook_url
DISCORD_WEBHOOK_ENABLED=true
DISCORD_NOTIFY_LOGINS=true

# License System
LICENSE_SESSION_SECRET=your_session_secret
LICENSE_FILE_PATH=licenses/license_data.enc
LICENSE_CACHE_HOURS=1
LICENSE_OFFLINE_GRACE_HOURS=24
```

### Installation Steps
1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Configure environment variables in `.env`
4. Run the application: `python app.py`
5. Access the platform at `http://localhost:5000`

## User Management

### Developer Hierarchy
- **Developers**: Highest level with full system access
- **Users**: Standard license holders with dashboard access

### License Format
License keys follow the format: `DEV-XXXXX-XXXX`
- `DEV`: Product identifier
- `XXXXX`: 5-digit license number
- `XXXX`: 4-character hexadecimal verification code

## API Endpoints

### Authentication
- `POST /login` - User authentication
- `GET /logout` - User logout
- `GET /admin` - Admin panel access

### License Management
- `POST /admin/add-license` - Create new license
- `POST /admin/remove-license` - Remove license
- `POST /admin/extend-license` - Extend license duration
- `POST /admin/edit-license` - Edit license details

### Developer Management
- `POST /admin/add-developer` - Promote user to developer
- `POST /admin/remove-developer` - Remove developer status
- `POST /admin/toggle-developer-protection` - Toggle protection status

## Security Features

### License Validation
- **Multi-factor Authentication**: Username + license key validation
- **GitHub Integration**: Secure license storage and validation
- **Session Management**: Secure session handling with timeout
- **Rate Limiting**: Protection against brute force attacks

### Access Control
- **Role-based Permissions**: Developer vs user access levels
- **Protected Developers**: Prevention of accidental removal
- **Admin Functions**: Restricted administrative capabilities

## Discord Integration

### Notification Types
- **License Events**: Creation, extension, removal notifications
- **User Activity**: Login notifications and session tracking
- **Developer Actions**: Promotion and management notifications
- **System Alerts**: Expiring licenses and system status

### Webhook Configuration
Configure Discord webhook URL in environment variables for real-time notifications to your Discord server.

## Pseudonyms under Greek Terminal

The following pseudonyms are associated with Greek Terminal development and operations:

| Pseudonym | Discord ID |
|-----------|------------|
| stackedeth | 1330639231500157149 |
| legogrievous | 564235489155088459 |
| vanagostine | 1087480323979350156 |
| akugo_v2 | 758321783731781643 |

## Support & Documentation

### Technical Support
- **Discord Community**: Join our Discord server for real-time assistance
- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Comprehensive guides and API documentation

### License Issues
- Verify license key format: `DEV-XXXXX-XXXX`
- Check GitHub repository access and permissions
- Ensure Discord ID is properly linked to your account

## Copyright

Copyright © 2025 Greek Terminal. All rights reserved.

## License

This software is proprietary and requires a valid license for use. Unauthorized distribution or use is prohibited.
