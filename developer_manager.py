#!/usr/bin/env python3
"""
Developer Management System for Greek Terminal
Manages developer status and permissions
"""

import os
import json
from datetime import datetime
from typing import List, Dict, Any

class DeveloperManager:
    """Manages developer status and permissions"""
    
    def __init__(self):
        self.developers_file = 'developers.json'
        self.developers = self._load_developers()

        # Migrate stackedeth from core admin to protected developer
        self._migrate_stackedeth_to_protected()
    
    def _load_developers(self) -> Dict[str, Any]:
        """Load developers from file"""
        try:
            if os.path.exists(self.developers_file):
                with open(self.developers_file, 'r') as f:
                    return json.load(f)
            else:
                # Initialize with admin users as developers
                admin_users = os.environ.get('LICENSE_ADMIN_USERS', '').split(',')
                initial_devs = {}
                for admin in admin_users:
                    admin = admin.strip()
                    if admin:
                        initial_devs[admin] = {
                            'username': admin,
                            'added_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'added_by': 'system',
                            'status': 'active',
                            'permanent': True
                        }
                
                # Save initial developers
                self._save_developers(initial_devs)
                return initial_devs
                
        except Exception as e:
            print(f"Error loading developers: {e}")
            return {}
    
    def _save_developers(self, developers: Dict[str, Any]) -> bool:
        """Save developers to file"""
        try:
            with open(self.developers_file, 'w') as f:
                json.dump(developers, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving developers: {e}")
            return False

    def _migrate_stackedeth_to_protected(self):
        """Migrate stackedeth from hardcoded core admin to protected developer"""
        try:
            # Check if stackedeth needs to be added as a protected developer
            if 'stackedeth' not in self.developers:
                # Add stackedeth as a protected developer
                self.developers['stackedeth'] = {
                    'username': 'stackedeth',
                    'added_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'added_by': 'system_migration',
                    'status': 'active',
                    'protected': True,
                    'migration_note': 'Migrated from core admin to protected developer'
                }

                # Save the updated developers
                if self._save_developers(self.developers):
                    print("🔄 Migrated stackedeth from core admin to protected developer")
                else:
                    print("⚠️ Failed to save stackedeth migration")
            else:
                # Ensure existing stackedeth is marked as protected
                if not self.developers['stackedeth'].get('protected', False):
                    self.developers['stackedeth']['protected'] = True
                    self.developers['stackedeth']['protection_changed_by'] = 'system_migration'
                    self.developers['stackedeth']['protection_changed_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    if self._save_developers(self.developers):
                        print("🛡️ Updated stackedeth to protected status")
                    else:
                        print("⚠️ Failed to update stackedeth protection status")

        except Exception as e:
            print(f"Error migrating stackedeth: {e}")

    def add_developer(self, username: str, added_by: str = 'admin', protected: bool = False) -> Dict[str, Any]:
        """Add a user as a developer"""
        try:
            if username in self.developers:
                return {
                    'success': False,
                    'error': f'{username} is already a developer'
                }

            # Add developer
            self.developers[username] = {
                'username': username,
                'added_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'added_by': added_by,
                'status': 'active',
                'protected': protected  # Protected developers cannot be removed
            }
            
            # Save to file
            if self._save_developers(self.developers):
                print(f"✅ Developer added: {username}")
                return {
                    'success': True,
                    'message': f'{username} has been promoted to developer'
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to save developer data'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def is_developer(self, username: str) -> bool:
        """Check if a user is a developer"""
        return username in self.developers and self.developers[username].get('status') == 'active'
    
    def get_developers(self) -> List[Dict[str, Any]]:
        """Get list of all developers"""
        return [dev_info for dev_info in self.developers.values() if dev_info.get('status') == 'active']
    
    def get_developer_usernames(self) -> List[str]:
        """Get list of developer usernames"""
        return [username for username, dev_info in self.developers.items() if dev_info.get('status') == 'active']
    
    def get_developer_info(self, username: str) -> Dict[str, Any]:
        """Get developer information"""
        return self.developers.get(username, {})
    
    def remove_developer(self, username: str, removed_by: str = 'admin') -> Dict[str, Any]:
        """Remove a developer (admin only operation with protection for core admins and protected developers)"""
        try:
            if username not in self.developers:
                return {
                    'success': False,
                    'error': f'{username} is not a developer'
                }

            dev_info = self.developers[username]

# Core admin protection removed - developers are the highest level

            # Check if developer is marked as protected
            if dev_info.get('protected', False):
                return {
                    'success': False,
                    'error': f'Cannot remove protected developer: {username}. This developer has been marked as protected.'
                }

            # Remove developer completely from the list
            removed_dev = self.developers.pop(username)

            if self._save_developers(self.developers):
                print(f"🗑️ Developer removed: {username} by {removed_by}")
                return {
                    'success': True,
                    'message': f'{username} has been removed from developer status',
                    'removed_developer': removed_dev
                }
            else:
                # Restore developer if save failed
                self.developers[username] = removed_dev
                return {
                    'success': False,
                    'error': 'Failed to save developer data'
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def toggle_protected_status(self, username: str, protected: bool, changed_by: str = 'admin') -> Dict[str, Any]:
        """Toggle protected status for a developer"""
        try:
            if username not in self.developers:
                return {
                    'success': False,
                    'error': f'{username} is not a developer'
                }

# Core admin protection removed - all developers can be managed equally

            # Update protected status
            old_status = self.developers[username].get('protected', False)
            self.developers[username]['protected'] = protected
            self.developers[username]['protection_changed_by'] = changed_by
            self.developers[username]['protection_changed_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            if self._save_developers(self.developers):
                status_text = "protected" if protected else "unprotected"
                print(f"🛡️ Developer {username} marked as {status_text} by {changed_by}")
                return {
                    'success': True,
                    'message': f'{username} has been marked as {status_text}',
                    'old_status': old_status,
                    'new_status': protected
                }
            else:
                # Restore old status if save failed
                self.developers[username]['protected'] = old_status
                return {
                    'success': False,
                    'error': 'Failed to save developer data'
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def get_stats(self) -> Dict[str, int]:
        """Get developer statistics"""
        active_devs = len([d for d in self.developers.values() if d.get('status') == 'active'])
        protected_devs = len([d for d in self.developers.values() if d.get('protected', False)])
        # No core admins - all developers are equal

        return {
            'total_developers': active_devs,
            'protected_developers': protected_devs,
            'removable_developers': active_devs - protected_devs
        }

# Global developer manager instance
_developer_manager = None

def get_developer_manager():
    """Get the global developer manager instance"""
    global _developer_manager
    if _developer_manager is None:
        _developer_manager = DeveloperManager()
    return _developer_manager

def is_developer(username: str) -> bool:
    """Quick check if a user is a developer"""
    manager = get_developer_manager()
    return manager.is_developer(username)

def get_developers_list() -> List[str]:
    """Get list of developer usernames"""
    manager = get_developer_manager()
    return manager.get_developer_usernames()

if __name__ == '__main__':
    # Test the developer manager
    manager = DeveloperManager()
    
    print("Current developers:")
    for dev in manager.get_developers():
        print(f"  - {dev['username']} (added: {dev['added_date']})")
    
    print(f"\nDeveloper stats: {manager.get_stats()}")
